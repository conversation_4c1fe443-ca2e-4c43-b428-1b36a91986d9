'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle } from 'lucide-react'
import { useAuthContext } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardBody } from '@/components/ui/Card'
import { Alert } from '@/components/ui/Alert'

export default function ResetPasswordPage() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null)

  const { updatePassword } = useAuthContext()
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleAuthFlow = async () => {
      // Check if we have the required tokens in the URL
      const accessToken = searchParams.get('access_token')
      const refreshToken = searchParams.get('refresh_token')
      const type = searchParams.get('type')
      const errorParam = searchParams.get('error')
      const errorDescription = searchParams.get('error_description')

      // Check for errors in URL
      if (errorParam) {
        setIsValidToken(false)
        setError(`Authentication error: ${errorDescription || errorParam}`)
        return
      }

      // Check if this is a recovery type
      if (type !== 'recovery') {
        setIsValidToken(false)
        setError('Invalid reset link type. Please request a new password reset.')
        return
      }

      if (!accessToken || !refreshToken) {
        setIsValidToken(false)
        setError('Invalid or missing reset token. Please request a new password reset.')
        return
      }

      try {
        // Set the session with the tokens from URL
        const { createClient } = await import('@/lib/supabase-client')
        const supabase = createClient()

        const { data, error: sessionError } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken
        })

        if (sessionError) {
          setIsValidToken(false)
          setError(`Session error: ${sessionError.message}`)
          return
        }

        if (data.session) {
          setIsValidToken(true)
        } else {
          setIsValidToken(false)
          setError('Failed to establish session. Please request a new password reset.')
        }
      } catch (err) {
        setIsValidToken(false)
        setError('An error occurred while validating the reset link.')
      }
    }

    handleAuthFlow()
  }, [searchParams])

  const validatePassword = (password: string): string | null => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long'
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number'
    }
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validate passwords
    if (!password || !confirmPassword) {
      setError('Please fill in all fields')
      setIsLoading(false)
      return
    }

    const passwordError = validatePassword(password)
    if (passwordError) {
      setError(passwordError)
      setIsLoading(false)
      return
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    try {
      const result = await updatePassword(password)

      if (result.success) {
        setSuccess(true)
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } else {
        setError(result.error || 'Failed to update password')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  // Show error state for invalid token
  if (isValidToken === false) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardBody className="pt-8">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl mb-4">
                  <AlertCircle className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-neutral-900 mb-2">
                  Invalid Reset Link
                </h1>
                <p className="text-neutral-600 mb-4">
                  This password reset link is invalid or has expired
                </p>
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-left">
                    <p className="text-red-800 text-sm font-medium">Error Details:</p>
                    <p className="text-red-700 text-sm mt-1">{error}</p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <Link href="/forgot-password">
                  <Button fullWidth>
                    Request New Reset Link
                  </Button>
                </Link>
                
                <Link href="/login">
                  <Button variant="secondary" fullWidth>
                    Back to Login
                  </Button>
                </Link>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    )
  }

  // Show success state
  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardBody className="pt-8">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-neutral-900 mb-2">
                  Password Updated!
                </h1>
                <p className="text-neutral-600">
                  Your password has been successfully updated
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <p className="text-green-800 text-sm">
                  You will be redirected to the login page in a few seconds...
                </p>
              </div>

              <Link href="/login">
                <Button fullWidth>
                  Continue to Login
                </Button>
              </Link>
            </CardBody>
          </Card>
        </div>
      </div>
    )
  }

  // Show loading state while checking token
  if (isValidToken === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardBody className="pt-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-neutral-600">Verifying reset link...</p>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardBody className="pt-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl mb-4">
                <Lock className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-neutral-900 mb-2">
                Reset Password
              </h1>
              <p className="text-neutral-600">
                Enter your new password below
              </p>
            </div>

            {error && (
              <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError('')}>
                {error}
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="relative">
                <Input
                  label="New Password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  leftIcon={<Lock className="w-5 h-5" />}
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-neutral-400 hover:text-neutral-600 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  }
                  placeholder="Enter new password"
                  required
                  disabled={isLoading}
                />
              </div>

              <div className="relative">
                <Input
                  label="Confirm New Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  leftIcon={<Lock className="w-5 h-5" />}
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="text-neutral-400 hover:text-neutral-600 transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  }
                  placeholder="Confirm new password"
                  required
                  disabled={isLoading}
                />
              </div>

              <div className="text-xs text-neutral-600 bg-neutral-50 p-3 rounded-lg">
                <p className="font-medium mb-1">Password requirements:</p>
                <ul className="space-y-1">
                  <li>• At least 8 characters long</li>
                  <li>• Contains uppercase and lowercase letters</li>
                  <li>• Contains at least one number</li>
                </ul>
              </div>

              <Button
                type="submit"
                loading={isLoading}
                fullWidth
                size="lg"
              >
                {isLoading ? 'Updating Password...' : 'Update Password'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <Link 
                href="/login"
                className="text-sm text-neutral-600 hover:text-neutral-800 transition-colors"
              >
                Back to Login
              </Link>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}
